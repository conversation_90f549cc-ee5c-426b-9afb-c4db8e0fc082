<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'Splitlines' => '/libs/jquery.splitlines.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Hammer_js' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js'
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HOME HEADER BLOCK' => '/blocks/js/block-home-header-block.js',
        'REVIEW SLIDER BLOCK' => '/blocks/js/block-review-slider-block.js',
        'LAST UPDATES BLOCK' => '/blocks/js/block-last-updates-block.js',
        'ABOUT HEADER BLOCK' => '/blocks/js/block-about-header-block.js',
        'SERVICES HEADER BLOCK' => '/blocks/js/block-services-header-block.js',
        'UPDATES HEADER BLOCK' => '/blocks/js/block-update-header-block.js',
        'CASE HEADER BLOCK' => '/blocks/js/block-case-header-block.js',
        'UPDATES BLOCK' => '/blocks/js/block-updates-block.js',
        'CASES BLOCK' => '/blocks/js/block-cases-block.js',
        'PARTNERS SLIDER BLOCK' => '/blocks/js/block-partners-slider-block.js'
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false) {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } elseif ($label === 'Description') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu',
        'secondary-menu' => 'Secondary Menu',
        'primary-footer-menu' => 'Primary Footer Menu',
        'secondary-footer-menu' => 'Secondary Footer Menu',
        'third-footer-menu' => 'Third Footer Menu',
        'fourth-footer-menu' => 'Fourth Footer Menu',
        'fifth-footer-menu' => 'Fifth Footer Menu'
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home header Block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'about',
            'title'             => __('Home about Block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/home-about-block.php',
            'category'          => 'about',
        ));
        acf_register_block_type(array(
            'name'              => 'review_slider_block',
            'title'             => __('Review slider block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/review-slider-block.php',
            'category'          => 'reviews',
        ));
        acf_register_block_type(array(
            'name'              => 'call_to_actions_block',
            'title'             => __('Call to actions block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/call-to-actions-block.php',
            'category'          => 'cta',
        ));
        acf_register_block_type(array(
            'name'              => 'last_updates_block',
            'title'             => __('Last updates block'),
            // 'description'       => __('A custom testimonial block.'),
            'render_template'   => 'blocks/last-updates-block.php',
            'category'          => 'updates',
        ));
        acf_register_block_type(array(
            'name'              => 'about_header_block',
            'title'             => __('About header block'),
            'render_template'   => 'blocks/about-header-block.php',
            'category'          => 'about',
        ));
        acf_register_block_type(array(
            'name'              => 'intro_text_block',
            'title'             => __('Intro text block'),
            'render_template'   => 'blocks/intro-text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'team_block',
            'title'             => __('Team block'),
            'render_template'   => 'blocks/team-block.php',
            'category'          => 'team',
        ));
        acf_register_block_type(array(
            'name'              => 'call_to_action_block',
            'title'             => __('Call to action block'),
            'render_template'   => 'blocks/call-to-action-block.php',
            'category'          => 'cta',
        ));
        acf_register_block_type(array(
            'name'              => 'update_header_block',
            'title'             => __('Update header block'),
            'render_template'   => 'blocks/update-header-block.php',
            'category'          => 'update',
        ));
        acf_register_block_type(array(
            'name'              => 'services_header_block',
            'title'             => __('Services header block'),
            'render_template'   => 'blocks/services-header-block.php',
            'category'          => 'services',
        ));
        acf_register_block_type(array(
            'name'              => 'image_text_block',
            'title'             => __('Image text block'),
            'render_template'   => 'blocks/image-text-block.php',
            'category'          => 'image',
        ));
        acf_register_block_type(array(
            'name'              => 'services_block',
            'title'             => __('Services block'),
            'render_template'   => 'blocks/services-block.php',
            'category'          => 'service',
        ));
        acf_register_block_type(array(
            'name'              => 'service_header_block',
            'title'             => __('Service header block'),
            'render_template'   => 'blocks/service-header-block.php',
            'category'          => 'service',
        ));
        acf_register_block_type(array(
            'name'              => 'downloads_block',
            'title'             => __('Downloads block'),
            'render_template'   => 'blocks/downloads-block.php',
            'category'          => 'downloads',
        ));
        acf_register_block_type(array(
            'name'              => 'members_by_amount_block',
            'title'             => __('Members by amount block'),
            'render_template'   => 'blocks/members-by-amount-block.php',
            'category'          => 'members,team',
        ));
        acf_register_block_type(array(
            'name'              => 'steps_block',
            'title'             => __('Steps block'),
            'render_template'   => 'blocks/steps-block.php',
            'category'          => 'steps',
        ));
        acf_register_block_type(array(
            'name'              => 'cases_block',
            'title'             => __('Cases block'),
            'render_template'   => 'blocks/cases-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'case_header_block',
            'title'             => __('Case header block'),
            'render_template'   => 'blocks/case-header-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'list_block',
            'title'             => __('List block'),
            'render_template'   => 'blocks/list-block.php',
            'category'          => 'list',
        ));
        acf_register_block_type(array(
            'name'              => 'two_images_block',
            'title'             => __('Two Images block'),
            'render_template'   => 'blocks/two-images-block.php',
            'category'          => 'images',
        ));
        acf_register_block_type(array(
            'name'              => 'case_footer_block',
            'title'             => __('Case footer block'),
            'render_template'   => 'blocks/case-footer-block.php',
            'category'          => 'cases',
        ));
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'header',
        ));
        acf_register_block_type(array(
            'name'              => 'big_image_block',
            'title'             => __('Big Image block'),
            'render_template'   => 'blocks/big-image-block.php',
            'category'          => 'image',
        ));
        acf_register_block_type(array(
            'name'              => 'vacature_header_block',
            'title'             => __('Vacature Header block'),
            'render_template'   => 'blocks/vacature-header-block.php',
            'category'          => 'vacature header block',
        ));
        acf_register_block_type(array(
            'name'              => 'updates_block',
            'title'             => __('Updates block'),
            'render_template'   => 'blocks/updates-block.php',
            'category'          => 'Updates block',
        ));
        acf_register_block_type(array(
            'name'              => 'contact_block',
            'title'             => __('Contact block'),
            'render_template'   => 'blocks/contact-block.php',
            'category'          => 'Contact block',
        ));
        acf_register_block_type(array(
            'name'              => 'vacatures_block',
            'title'             => __('Vacatures block'),
            'render_template'   => 'blocks/vacatures-block.php',
            'category'          => 'Vacatures block',
        ));
        acf_register_block_type(array(
            'name'              => 'partners_slider_block',
            'title'             => __('Partners slider block'),
            'render_template'   => 'blocks/partners-slider-block.php',
            'category'          => 'Partners slider block',
        ));
        acf_register_block_type(array(
            'name'              => 'latest_cases_block',
            'title'             => __('Latest cases block'),
            'render_template'   => 'blocks/latest-cases-block.php',
            'category'          => 'Latest cases block',
        ));
        acf_register_block_type(array(
            'name'              => 'companions_block',
            'title'             => __('Companions block'),
            'render_template'   => 'blocks/companions-block.php',
            'category'          => 'Companions block',
        ));
    }
}

?>
